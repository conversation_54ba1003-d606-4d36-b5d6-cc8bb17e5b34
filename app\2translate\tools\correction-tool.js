/**
 * Correction Tool
 * 
 * Provides iterative improvement and correction capabilities for translations.
 * This tool analyzes translated content and suggests improvements for:
 * - Grammar and syntax correctness
 * - Natural Polish language flow
 * - Character consistency
 * - Cultural adaptation
 * - Emotional tone preservation
 */

import Anthropic from '@anthropic-ai/sdk';

export class CorrectionTool {
  constructor(options = {}) {
    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });
    
    this.model = "claude-sonnet-4-20250514";
    this.maxIterations = options.maxIterations || 2;
    this.qualityThreshold = options.qualityThreshold || 0.8;
    this.enableGrammarCheck = options.enableGrammarCheck !== false;
    this.enableConsistencyCheck = options.enableConsistencyCheck !== false;
    this.enableCulturalAdaptation = options.enableCulturalAdaptation !== false;
    
    console.log('[CorrectionTool] Initialized with iterative improvement capabilities');
  }

  /**
   * Improve translation through iterative correction
   * @param {string} originalText - Original English text
   * @param {string} translatedText - Initial Polish translation
   * @param {Object} context - Translation context
   * @returns {Promise<string>} - Improved translation
   */
  async improveTranslation(originalText, translatedText, context = {}) {
    try {
      console.log('[CorrectionTool] Starting translation improvement process...');
      console.log(`[CorrectionTool] Original text: "${originalText.substring(0, 100)}..."`);
      console.log(`[CorrectionTool] Initial translation: "${translatedText.substring(0, 100)}..."`);

      let currentTranslation = translatedText;
      let iteration = 0;

      while (iteration < this.maxIterations) {
        iteration++;
        console.log(`[CorrectionTool] Improvement iteration ${iteration}/${this.maxIterations}`);

        // Analyze current translation quality
        const analysis = await this.analyzeTranslationQuality(
          originalText,
          currentTranslation,
          context
        );

        console.log(`[CorrectionTool] Quality analysis results:`);
        console.log(`  Grammar: ${analysis.grammar_score}`);
        console.log(`  Naturalness: ${analysis.naturalness_score}`);
        console.log(`  Accuracy: ${analysis.accuracy_score}`);
        console.log(`  Consistency: ${analysis.consistency_score}`);
        console.log(`  Cultural Adaptation: ${analysis.cultural_adaptation_score}`);
        console.log(`  Overall Score: ${analysis.overallScore}`);
        console.log(`  Issues found: ${analysis.issues?.length || 0}`);

        if (analysis.issues && analysis.issues.length > 0) {
          console.log(`[CorrectionTool] Issues detected:`);
          analysis.issues.forEach((issue, i) => {
            console.log(`    ${i + 1}. ${issue.type}: ${issue.description} (${issue.severity})`);
          });
        }

        // If quality is good enough, stop iterating
        if (analysis.overallScore >= this.qualityThreshold) {
          console.log('[CorrectionTool] Quality threshold reached, stopping iterations');
          break;
        }

        // Generate improvements based on analysis
        const improvedTranslation = await this.generateImprovements(
          originalText,
          currentTranslation,
          analysis,
          context
        );

        if (improvedTranslation && improvedTranslation !== currentTranslation) {
          currentTranslation = improvedTranslation;
          console.log('[CorrectionTool] Translation improved');
          console.log(`[CorrectionTool] New translation: "${improvedTranslation.substring(0, 100)}..."`);
        } else {
          console.log('[CorrectionTool] No further improvements possible');
          break;
        }
      }

      return currentTranslation;

    } catch (error) {
      console.error('[CorrectionTool] Improvement failed:', error.message);
      console.error('[CorrectionTool] Error stack:', error.stack);
      return translatedText; // Return original translation if improvement fails
    }
  }

  /**
   * Analyze translation quality across multiple dimensions
   * @param {string} original - Original text
   * @param {string} translation - Translated text
   * @param {Object} context - Context information
   * @returns {Promise<Object>} - Quality analysis
   */
  async analyzeTranslationQuality(original, translation, context) {
    console.log('[CorrectionTool] Starting quality analysis...');

    const tools = [
      {
        name: "analyze_translation_quality",
        description: "Analyze translation quality across multiple dimensions",
        input_schema: {
          type: "object",
          properties: {
            grammar_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Grammar correctness score (0-1)"
            },
            naturalness_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Natural Polish language flow score (0-1)"
            },
            accuracy_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Meaning preservation accuracy score (0-1)"
            },
            consistency_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Character and terminology consistency score (0-1)"
            },
            cultural_adaptation_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Cultural adaptation appropriateness score (0-1)"
            },
            issues: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  type: { type: "string", enum: ["grammar", "naturalness", "accuracy", "consistency", "cultural"] },
                  description: { type: "string" },
                  severity: { type: "string", enum: ["low", "medium", "high"] },
                  line_number: { type: "number" },
                  suggestion: { type: "string" }
                },
                required: ["type", "description", "severity"]
              },
              description: "List of identified issues"
            }
          },
          required: ["grammar_score", "naturalness_score", "accuracy_score", "consistency_score", "cultural_adaptation_score", "issues"]
        }
      }
    ];

    const prompt = this.buildQualityAnalysisPrompt(original, translation, context);
    console.log('[CorrectionTool] Sending quality analysis request to Claude 4...');

    try {
      const response = await this.anthropic.messages.create({
        model: this.model,
        max_tokens: 4096,
        temperature: 0.3,
        messages: [{ role: 'user', content: prompt }],
        tools: tools,
        tool_choice: { type: "tool", name: "analyze_translation_quality" }
      });

      console.log('[CorrectionTool] Received response from Claude 4');
      console.log('[CorrectionTool] Response content types:', response.content.map(c => c.type));

      // Extract analysis from tool call
      const toolCall = response.content.find(c => c.type === 'tool_use');
      if (toolCall && toolCall.input) {
        console.log('[CorrectionTool] Found tool call with input');
        console.log('[CorrectionTool] Raw tool input:', JSON.stringify(toolCall.input, null, 2));

        const analysis = toolCall.input;
        analysis.overallScore = this.calculateOverallScore(analysis);
        console.log('[CorrectionTool] Calculated overall score:', analysis.overallScore);
        return analysis;
      } else {
        console.warn('[CorrectionTool] No tool call found in response');
        console.log('[CorrectionTool] Full response:', JSON.stringify(response.content, null, 2));
      }

    } catch (error) {
      console.error('[CorrectionTool] API call failed:', error.message);
      console.error('[CorrectionTool] Error details:', error);
    }

    // Fallback analysis if tool call fails
    console.log('[CorrectionTool] Using fallback analysis');
    return {
      grammar_score: 0.7,
      naturalness_score: 0.7,
      accuracy_score: 0.8,
      consistency_score: 0.7,
      cultural_adaptation_score: 0.7,
      overallScore: 0.72,
      issues: []
    };
  }

  /**
   * Generate improvements based on quality analysis
   * @param {string} original - Original text
   * @param {string} translation - Current translation
   * @param {Object} analysis - Quality analysis
   * @param {Object} context - Context information
   * @returns {Promise<string>} - Improved translation
   */
  async generateImprovements(original, translation, analysis, context) {
    const tools = [
      {
        name: "improve_translation",
        description: "Generate improved translation addressing identified issues",
        input_schema: {
          type: "object",
          properties: {
            improved_translation: {
              type: "string",
              description: "Improved Polish translation addressing the identified issues"
            },
            changes_made: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  line_number: { type: "number" },
                  original_line: { type: "string" },
                  improved_line: { type: "string" },
                  reason: { type: "string" }
                },
                required: ["original_line", "improved_line", "reason"]
              },
              description: "List of specific changes made"
            }
          },
          required: ["improved_translation", "changes_made"]
        }
      }
    ];

    const prompt = this.buildImprovementPrompt(original, translation, analysis, context);
    
    const response = await this.anthropic.messages.create({
      model: this.model,
      max_tokens: 4096,
      temperature: 0.5,
      messages: [{ role: 'user', content: prompt }],
      tools: tools,
      tool_choice: { type: "tool", name: "improve_translation" }
    });

    // Extract improved translation from tool call
    const toolCall = response.content.find(c => c.type === 'tool_use');
    if (toolCall && toolCall.input && toolCall.input.improved_translation) {
      console.log(`[CorrectionTool] Made ${toolCall.input.changes_made?.length || 0} improvements`);
      return toolCall.input.improved_translation;
    }

    return null;
  }

  /**
   * Build prompt for quality analysis
   * @param {string} original - Original text
   * @param {string} translation - Translation to analyze
   * @param {Object} context - Context information
   * @returns {string} - Analysis prompt
   */
  buildQualityAnalysisPrompt(original, translation, context) {
    return `Please analyze the quality of this Polish translation of anime dialogue:

ORIGINAL ENGLISH:
${original}

POLISH TRANSLATION:
${translation}

CONTEXT:
- Characters: ${context.characters?.join(', ') || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}
- Emotional tone: ${context.emotionalTone || 'Unknown'}
- Previous translations: ${context.previousTranslations ? 'Available' : 'None'}

Please evaluate the translation across these dimensions:
1. Grammar correctness (Polish grammar rules, case declension, verb aspects)
2. Naturalness (how natural it sounds to Polish speakers)
3. Accuracy (preservation of original meaning and nuance)
4. Consistency (character speech patterns, terminology)
5. Cultural adaptation (appropriate for Polish anime audience)

Identify specific issues and provide scores for each dimension.`;
  }

  /**
   * Build prompt for generating improvements
   * @param {string} original - Original text
   * @param {string} translation - Current translation
   * @param {Object} analysis - Quality analysis
   * @param {Object} context - Context information
   * @returns {string} - Improvement prompt
   */
  buildImprovementPrompt(original, translation, analysis, context) {
    const issuesSummary = analysis.issues
      .map(issue => `- ${issue.type}: ${issue.description} (${issue.severity})`)
      .join('\n');

    return `Please improve this Polish translation by addressing the identified issues:

ORIGINAL ENGLISH:
${original}

CURRENT POLISH TRANSLATION:
${translation}

IDENTIFIED ISSUES:
${issuesSummary}

QUALITY SCORES:
- Grammar: ${analysis.grammar_score}
- Naturalness: ${analysis.naturalness_score}
- Accuracy: ${analysis.accuracy_score}
- Consistency: ${analysis.consistency_score}
- Cultural adaptation: ${analysis.cultural_adaptation_score}

CONTEXT:
- Characters: ${context.characters?.join(', ') || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}
- Emotional tone: ${context.emotionalTone || 'Unknown'}

Please provide an improved translation that:
1. Maintains the exact number of lines
2. Preserves speaker information and formatting
3. Addresses the identified issues
4. Sounds natural in Polish
5. Maintains character consistency

Focus on the most critical issues first, especially those marked as "high" severity.`;
  }

  /**
   * Calculate overall quality score from individual scores
   * @param {Object} analysis - Quality analysis
   * @returns {number} - Overall score (0-1)
   */
  calculateOverallScore(analysis) {
    const weights = {
      grammar_score: 0.25,
      naturalness_score: 0.25,
      accuracy_score: 0.25,
      consistency_score: 0.15,
      cultural_adaptation_score: 0.10
    };

    let weightedSum = 0;
    let totalWeight = 0;

    for (const [key, weight] of Object.entries(weights)) {
      if (typeof analysis[key] === 'number') {
        weightedSum += analysis[key] * weight;
        totalWeight += weight;
      }
    }

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  /**
   * Check specific grammar rules for Polish
   * @param {string} text - Text to check
   * @returns {Array} - Array of grammar issues
   */
  checkPolishGrammar(text) {
    const issues = [];
    const lines = text.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Check for common Polish grammar issues
      if (this.enableGrammarCheck) {
        // Check for incorrect honorific inflection
        if (/-sam[aąęiej]/.test(line) && !/-sama$/.test(line)) {
          issues.push({
            type: 'grammar',
            description: 'Japanese honorific incorrectly inflected',
            severity: 'medium',
            line_number: i + 1,
            suggestion: 'Keep honorifics like -sama, -chan, -kun uninflected'
          });
        }

        // Check for missing commas before "że"
        if (/\s+że\s+/.test(line) && !/,\s*że/.test(line)) {
          issues.push({
            type: 'grammar',
            description: 'Missing comma before "że"',
            severity: 'low',
            line_number: i + 1,
            suggestion: 'Add comma before "że"'
          });
        }
      }
    }

    return issues;
  }

  /**
   * Check translation consistency
   * @param {string} text - Text to check
   * @param {Object} context - Context with previous translations
   * @returns {Array} - Array of consistency issues
   */
  checkConsistency(text, context) {
    const issues = [];
    
    if (this.enableConsistencyCheck && context.characterTerminology) {
      // Check for consistent character name translations
      // This would be expanded with actual consistency checking logic
    }

    return issues;
  }
}
