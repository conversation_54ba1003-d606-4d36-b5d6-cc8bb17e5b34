// @ts-check

/**
 * Claude 4 Sonnet Translation Engine with Tool Use
 * 
 * This module provides an advanced translation system that utilizes Claude 4 Sonnet
 * with tool use capabilities for intelligent anime subtitle translation.
 * 
 * Features:
 * - Intelligent scene detection for dynamic chunking
 * - Screenshot analysis for visual context
 * - Iterative correction and improvement
 * - Context-aware translation with character consistency
 * - Polish language optimization
 */

import Anthropic from '@anthropic-ai/sdk';
import { SceneDetector } from './tools/scene-detector.js';
import { ScreenshotTool } from './tools/screenshot-tool.js';
import { CorrectionTool } from './tools/correction-tool.js';
import { ContextManager } from './context-manager.js';
import { getValidatedConfig } from './config.js';
import fs from 'fs';
import path from 'path';

export class Claude4Translator {
  constructor(options = {}) {
    // Load and merge configuration
    const config = getValidatedConfig(process.env.NODE_ENV || 'production');
    this.config = { ...config, ...options };

    this.anthropic = new Anthropic({
      apiKey: process.env.ANTHROPIC_API_KEY,
    });

    this.model = this.config.model;
    this.maxTokens = this.config.maxTokens;
    this.temperature = this.config.temperature;

    // Initialize tools with configuration
    this.sceneDetector = new SceneDetector(this.config.sceneDetection);
    this.screenshotTool = new ScreenshotTool(this.config.screenshot);
    this.correctionTool = new CorrectionTool(this.config.correction);
    this.contextManager = new ContextManager();

    // Translation settings
    this.maxRetries = this.config.maxRetries;
    this.enableScreenshots = this.config.enableScreenshots;
    this.enableCorrection = this.config.enableCorrection;

    console.log(`[Claude4Translator] Initialized with model: ${this.model}`);
  }

  /**
   * Main translation method that processes subtitle content with intelligent chunking
   * @param {string} subtitleContent - Raw subtitle content
   * @param {string|null} videoPath - Path to video file for screenshots (optional)
   * @param {Object} metadata - Anime metadata (title, characters, genres)
   * @returns {Promise<string>} - Translated content
   */
  async translateSubtitles(subtitleContent, videoPath = null, metadata = {}) {
    try {
      console.log('[Claude4Translator] Starting translation process...');
      
      // Initialize context with metadata
      this.contextManager.initialize(metadata);
      
      // Detect scenes and create intelligent chunks
      const scenes = await this.sceneDetector.detectScenes(subtitleContent);
      console.log(`[Claude4Translator] Detected ${scenes.length} scenes`);
      
      let translatedContent = '';
      
      for (let i = 0; i < scenes.length; i++) {
        const scene = scenes[i];
        console.log(`[Claude4Translator] Processing scene ${i + 1}/${scenes.length} (${scene.lines.length} lines)`);
        
        // Get visual context if enabled
        let visualContext = null;
        if (this.enableScreenshots && videoPath && scene.timestamp) {
          try {
            visualContext = await this.screenshotTool.captureFrame(videoPath, scene.timestamp);
          } catch (error) {
            console.warn(`[Claude4Translator] Screenshot failed for scene ${i + 1}: ${error.message}`);
          }
        }
        
        // Translate the scene
        const translatedScene = await this.translateScene(scene, visualContext, i);
        
        // Apply corrections if enabled
        if (this.enableCorrection) {
          const correctedScene = await this.correctionTool.improveTranslation(
            scene.content,
            translatedScene,
            this.contextManager.getContext()
          );
          translatedContent += correctedScene + '\n';
        } else {
          translatedContent += translatedScene + '\n';
        }
        
        // Update context with translated content
        this.contextManager.updateContext(scene, translatedScene);
      }
      
      return translatedContent.trim();
      
    } catch (error) {
      console.error('[Claude4Translator] Translation failed:', error);
      throw error;
    }
  }

  /**
   * Translate a single scene using Claude 4 with tool use
   * @param {Object} scene - Scene object with content and metadata
   * @param {Object|null} visualContext - Screenshot data if available
   * @param {number} sceneIndex - Index of current scene
   * @returns {Promise<string>} - Translated scene content
   */
  async translateScene(scene, visualContext, sceneIndex) {
    const tools = this.getTranslationTools();
    const systemPrompt = this.buildSystemPrompt();
    const userPrompt = this.buildUserPrompt(scene, visualContext, sceneIndex);

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        const response = await this.anthropic.messages.create({
          model: this.model,
          max_tokens: this.maxTokens,
          temperature: this.temperature,
          system: systemPrompt,
          messages: [
            {
              role: 'user',
              content: userPrompt
            }
          ],
          tools: tools,
          tool_choice: { type: "auto" }
        });

        // Process the response and handle tool calls
        return await this.processTranslationResponse(response, scene);

      } catch (error) {
        console.warn(`[Claude4Translator] Translation attempt ${attempt} failed:`, error.message);
        if (attempt === this.maxRetries) {
          console.error(`[Claude4Translator] All translation attempts failed, returning original content`);
          return scene.content; // Return original content as fallback
        }
        await this.sleep(2000 * attempt); // Exponential backoff
      }
    }

    // This should never be reached, but included for safety
    return scene.content;
  }

  /**
   * Define tools available for translation
   * @returns {Array} - Array of tool definitions
   */
  getTranslationTools() {
    return [
      {
        name: "analyze_scene_context",
        description: "Analyze the scene context to understand dialogue flow, character relationships, and emotional tone",
        input_schema: {
          type: "object",
          properties: {
            speakers: {
              type: "array",
              items: { type: "string" },
              description: "List of speakers in this scene"
            },
            scene_type: {
              type: "string",
              enum: ["dialogue", "monologue", "action", "comedy", "dramatic", "exposition"],
              description: "Type of scene being translated"
            },
            emotional_tone: {
              type: "string",
              description: "Overall emotional tone of the scene"
            },
            cultural_references: {
              type: "array",
              items: { type: "string" },
              description: "Any cultural references or idioms that need special handling"
            }
          },
          required: ["speakers", "scene_type", "emotional_tone"]
        }
      },
      {
        name: "translate_with_context",
        description: "Translate dialogue lines with full context awareness and Polish language optimization",
        input_schema: {
          type: "object",
          properties: {
            translations: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  original: { type: "string", description: "Original line" },
                  translation: { type: "string", description: "Polish translation" },
                  speaker: { type: "string", description: "Character speaking" },
                  notes: { type: "string", description: "Translation notes or reasoning" }
                },
                required: ["original", "translation", "speaker"]
              },
              description: "Array of translated dialogue lines"
            }
          },
          required: ["translations"]
        }
      },
      {
        name: "request_visual_context",
        description: "Request additional visual context when translation requires understanding of visual elements",
        input_schema: {
          type: "object",
          properties: {
            timestamp: { type: "string", description: "Timestamp for screenshot" },
            reason: { type: "string", description: "Why visual context is needed" }
          },
          required: ["timestamp", "reason"]
        }
      }
    ];
  }

  /**
   * Build system prompt for Claude 4
   * @returns {string} - System prompt
   */
  buildSystemPrompt() {
    return `You are an expert anime subtitle translator specializing in English to Polish translation. You have access to tools that help you understand scene context, analyze visual elements, and produce high-quality translations.

Your translation approach should:
1. Use the analyze_scene_context tool first to understand the scene
2. Consider character relationships, emotional tone, and cultural context
3. Use translate_with_context tool to produce natural Polish translations
4. Request visual context when needed for better understanding
5. Maintain consistency with previous translations in the same episode

Polish Language Guidelines:
- Use natural Polish sentence structure and word order
- Preserve Japanese honorifics (san, chan, kun, sama) without inflection
- Adapt idioms and cultural references for Polish audiences
- Use appropriate formality levels based on character relationships
- Avoid literal translations of phrasal verbs and expressions
- Ensure proper case declension and verb aspects

Quality Standards:
- Maintain the exact number of dialogue lines
- Preserve timing and speaker information
- Keep translations natural and engaging
- Ensure consistency in character speech patterns
- Adapt humor and wordplay when possible`;
  }

  /**
   * Build user prompt for specific scene
   * @param {Object} scene - Scene to translate
   * @param {Object|null} visualContext - Visual context if available
   * @param {number} sceneIndex - Scene index
   * @returns {string} - User prompt
   */
  buildUserPrompt(scene, visualContext, sceneIndex) {
    let prompt = `Please translate this anime scene (Scene ${sceneIndex + 1}) from English to Polish:

SCENE CONTENT:
${scene.content}

SCENE METADATA:
- Timestamp: ${scene.timestamp || 'Unknown'}
- Duration: ${scene.duration || 'Unknown'}
- Line count: ${scene.lines.length}

CONTEXT:
${this.contextManager.getContextSummary()}`;

    if (visualContext) {
      prompt += `\n\nVISUAL CONTEXT:
A screenshot has been captured at timestamp ${scene.timestamp} to provide visual context for this scene.`;
    }

    prompt += `\n\nPlease use your tools to analyze and translate this scene appropriately.`;

    return prompt;
  }

  /**
   * Process Claude's response and handle tool calls
   * @param {Object} response - Claude's response
   * @param {Object} scene - Original scene
   * @returns {Promise<string>} - Final translated content
   */
  async processTranslationResponse(response, scene) {
    let finalTranslation = '';
    
    for (const content of response.content) {
      if (content.type === 'text') {
        finalTranslation += content.text;
      } else if (content.type === 'tool_use') {
        const toolResult = await this.handleToolCall(content, scene);
        if (toolResult && typeof toolResult === 'string') {
          finalTranslation += toolResult;
        }
      }
    }
    
    return finalTranslation || scene.content; // Fallback to original if translation fails
  }

  /**
   * Handle individual tool calls
   * @param {Object} toolCall - Tool call from Claude
   * @param {Object} _scene - Current scene (unused but kept for future use)
   * @returns {Promise<string|null>} - Tool result
   */
  async handleToolCall(toolCall, _scene) {
    const { name, input } = toolCall;

    switch (name) {
      case 'analyze_scene_context':
        this.contextManager.updateSceneAnalysis(input);
        return null; // Analysis doesn't return translation content

      case 'translate_with_context':
        return this.formatTranslations(input.translations);

      case 'request_visual_context':
        // This would trigger additional screenshot capture if needed
        console.log(`[Claude4Translator] Visual context requested: ${input.reason}`);
        return null;

      default:
        console.warn(`[Claude4Translator] Unknown tool: ${name}`);
        return null;
    }
  }

  /**
   * Format translations into final output
   * @param {Array} translations - Array of translation objects
   * @returns {string} - Formatted translation content
   */
  formatTranslations(translations) {
    return translations
      .map(t => `${t.speaker}: ${t.translation}`)
      .join('\n');
  }

  /**
   * Sleep utility
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
