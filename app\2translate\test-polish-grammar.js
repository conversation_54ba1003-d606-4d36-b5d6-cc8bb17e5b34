// @ts-check

/**
 * Test Polish Grammar Checking
 * 
 * This script tests the enhanced Polish grammar checking capabilities
 * with various common errors and punctuation mistakes.
 */

import { config } from 'dotenv';
config();

import { Claude4Translator } from './claude4-translator.js';

console.log('🧪 Testing Enhanced Polish Grammar Checking...\n');

// Test cases with common Polish errors
const testCases = [
  {
    name: "Punctuation Errors (High Priority)",
    text: `akira: <PERSON><PERSON><PERSON><PERSON> że to dobry pomysł.
yuki: Chcę żeby wszystko było gotowe.
sensei: Proszę aby wszyscy byli cicho.
akira: Nie mogę bo jestem zajęty.
yuki: Tak ale nie wiem czy to możliwe.
narrator: <PERSON><PERSON><PERSON><PERSON>, i wrócił szybko.`
  },
  {
    name: "Honorific Inflection Errors",
    text: `akira: <PERSON><PERSON>-chanie, czy jesteś gotowa?
yuki: <PERSON><PERSON>, <PERSON>-kunie!
sensei: <PERSON>-samą proszę do tablicy.`
  },
  {
    name: "Common Polish Grammar Errors",
    text: `akira: Spotkajmy się na rano.
yuki: <PERSON><PERSON> wiesz czy on przyjdzie?
sensei: <PERSON><PERSON><PERSON> my<PERSON> ręce przed jedzeniem.
akira: Mam 3 koty w domu.`
  },
  {
    name: "Spelling and Usage Errors",
    text: `akira: Wogóle nie rozumiem.
yuki: Narazie zostanę w domu.
sensei: To nie możliwe!
akira: To nie robi sens.
yuki: Czuję się komfortowo tutaj.`
  },
  {
    name: "Redundant Expressions",
    text: `akira: Mam bardzo dużo pracy.
yuki: Jestem całkowicie kompletnie zmęczona.`
  },
  {
    name: "Capitalization Errors",
    text: `akira: Spotkajmy się w Poniedziałek.
yuki: Luty to zimny miesiąc.`
  }
];

async function testGrammarChecking() {
  try {
    const translator = new Claude4Translator({
      enableScreenshots: false,
      enableCorrection: true, // Enable to see grammar checking in action
      maxRetries: 1
    });

    for (const testCase of testCases) {
      console.log(`\n🔍 Testing: ${testCase.name}`);
      console.log(`📝 Input text:`);
      console.log(`${testCase.text}\n`);

      try {
        const result = await translator.translateSubtitles(
          testCase.text,
          null,
          { title: 'Grammar Test', characters: [{ name: 'akira' }, { name: 'yuki' }, { name: 'sensei' }] }
        );

        console.log(`✅ Translation completed`);
        console.log(`📄 Result:`);
        console.log(`${result}\n`);
        
      } catch (error) {
        console.error(`❌ Test failed for "${testCase.name}": ${error.message}\n`);
      }

      // Add a small delay between tests
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    console.log('\n🎉 Grammar checking tests completed!');
    console.log('\n📊 Summary of Enhanced Grammar Checks:');
    console.log('✅ Punctuation errors (missing commas before że, żeby, aby, bo, ale)');
    console.log('✅ Incorrect commas (before "i", "oraz")');
    console.log('✅ Japanese honorific inflection errors');
    console.log('✅ Common Polish grammar mistakes');
    console.log('✅ Preposition errors (na vs w with time)');
    console.log('✅ Reflexive verb errors (missing "się")');
    console.log('✅ Number case agreement errors');
    console.log('✅ Spelling errors (wogóle, narazie)');
    console.log('✅ English calques (robić sens, czuć się komfortowo)');
    console.log('✅ Redundant expressions');
    console.log('✅ Capitalization errors (months, days)');

  } catch (error) {
    console.error('❌ Grammar testing failed:', error.message);
  }
}

// Test just the grammar checking function directly
async function testGrammarFunction() {
  console.log('\n🔧 Testing Grammar Function Directly...\n');
  
  const translator = new Claude4Translator({
    enableScreenshots: false,
    enableCorrection: false
  });

  const testText = `akira: Myślę że to dobry pomysł, i pójdę tam.
yuki: Chcę żeby Yuki-chanie była szczęśliwa.
sensei: Wogóle nie rozumiem tego.`;

  const issues = translator.correctionTool.checkPolishGrammar(testText);
  
  console.log(`🔍 Found ${issues.length} grammar issues:`);
  issues.forEach((issue, i) => {
    const severityColor = issue.severity === 'high' ? '🔴' : 
                         issue.severity === 'medium' ? '🟡' : '🟢';
    console.log(`${severityColor} ${i + 1}. Line ${issue.line_number}: ${issue.description}`);
    console.log(`   💡 Suggestion: ${issue.suggestion}\n`);
  });
}

// Run tests
async function runAllTests() {
  await testGrammarFunction();
  
  if (process.env.ANTHROPIC_API_KEY) {
    await testGrammarChecking();
  } else {
    console.log('\n⏭️ Skipping full translation tests (no API key)');
    console.log('💡 Set ANTHROPIC_API_KEY to test full translation with grammar checking');
  }
}

runAllTests().catch(console.error);
